<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <Button
        android:id="@+id/btnConnect"
        android:text="Connect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <LinearLayout
        android:orientation="vertical"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp">

        <Button
            android:id="@+id/btnUp"
            android:text="Up"
            android:layout_width="100dp"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/btnLeft"
                android:text="Left"
                android:layout_width="100dp"
                android:layout_height="wrap_content"/>

            <Button
                android:id="@+id/btnStop"
                android:text="Stop"
                android:layout_width="100dp"
                android:layout_height="wrap_content"/>

            <Button
                android:id="@+id/btnRight"
                android:text="Right"
                android:layout_width="100dp"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <Button
            android:id="@+id/btnDown"
            android:text="Down"
            android:layout_width="100dp"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</LinearLayout> 