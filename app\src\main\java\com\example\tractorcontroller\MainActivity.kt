package com.example.tractorcontroller

import android.Manifest
import android.app.Activity
import android.app.AlertDialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothSocket
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import java.io.IOException
import java.util.*

class MainActivity : AppCompatActivity() {
    private val REQUEST_ENABLE_BT = 1
    private val REQUEST_PERMISSIONS = 2
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var bluetoothSocket: BluetoothSocket? = null
    private var isConnected = false
    private var device: BluetoothDevice? = null
    private val UUID_STRING = "00001101-0000-1000-8000-00805F9B34FB" // SPP UUID

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        if (bluetoothAdapter == null) {
            Toast.makeText(this, "Bluetooth not supported", Toast.LENGTH_LONG).show()
            finish()
        }

        // Request permissions for Android 12+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
                ),
                REQUEST_PERMISSIONS
            )
        }

        findViewById<Button>(R.id.btnConnect).setOnClickListener { connectBluetooth() }
        findViewById<Button>(R.id.btnUp).setOnClickListener { sendCommand('F') }
        findViewById<Button>(R.id.btnDown).setOnClickListener { sendCommand('B') }
        findViewById<Button>(R.id.btnLeft).setOnClickListener { sendCommand('L') }
        findViewById<Button>(R.id.btnRight).setOnClickListener { sendCommand('R') }
        findViewById<Button>(R.id.btnStop).setOnClickListener { sendCommand('S') }
            }

    private fun connectBluetooth() {
        if (isConnected) {
            Toast.makeText(this, "Already connected", Toast.LENGTH_SHORT).show()
            return
        }
        if (bluetoothAdapter?.isEnabled == false) {
            val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT)
            return
        }
        val pairedDevices = bluetoothAdapter?.bondedDevices
        if (pairedDevices.isNullOrEmpty()) {
            Toast.makeText(this, "No paired devices found", Toast.LENGTH_SHORT).show()
            return
        }
        // Show device picker dialog
        val deviceNames = pairedDevices.map { it.name + "\n" + it.address }.toTypedArray()
        AlertDialog.Builder(this)
            .setTitle("Select Device")
            .setItems(deviceNames) { _, which ->
                device = pairedDevices.elementAt(which)
                connectToDevice(device!!)
            }
            .show()
    }

    private fun connectToDevice(device: BluetoothDevice) {
        Thread {
            try {
                val uuid = UUID.fromString(UUID_STRING)
                bluetoothSocket = device.createRfcommSocketToServiceRecord(uuid)
                bluetoothAdapter?.cancelDiscovery()
                bluetoothSocket?.connect()
                runOnUiThread {
                    isConnected = true
                    Toast.makeText(this, "Connected to ${device.name}", Toast.LENGTH_SHORT).show()
                }
            } catch (e: IOException) {
                runOnUiThread {
                    Toast.makeText(this, "Connection failed: ${e.message}", Toast.LENGTH_LONG).show()
                }
                try { bluetoothSocket?.close() } catch (_: IOException) {}
                bluetoothSocket = null
                isConnected = false
            }
        }.start()
    }

    private fun sendCommand(cmd: Char) {
        if (!isConnected || bluetoothSocket == null) {
            Toast.makeText(this, "Not connected", Toast.LENGTH_SHORT).show()
            return
        }
        try {
            bluetoothSocket?.outputStream?.write(cmd.code)
        } catch (e: IOException) {
            Toast.makeText(this, "Send failed: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try { bluetoothSocket?.close() } catch (_: IOException) {}
    }
}